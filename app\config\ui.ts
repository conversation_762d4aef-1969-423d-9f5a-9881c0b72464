// UI component configuration
// This file contains configuration for UI components, themes, and styling

// shadcn/ui configuration (derived from components.json)
export const uiConfig = {
  style: "default",
  rsc: false,
  tsx: true,
  tailwind: {
    config: "tailwind.config.ts",
    css: "app/tailwind.css",
    baseColor: "slate",
    cssVariables: true,
    prefix: "",
  },
  aliases: {
    components: "@/components",
    utils: "@/lib/utils",
    ui: "@/components/ui",
    lib: "@/lib",
    hooks: "@/hooks",
  },
} as const;

// Theme configuration removed - no longer using theme switching

// Component default props and variants
export const componentDefaults = {
  button: {
    size: "default",
    variant: "default",
  },
  input: {
    size: "default",
    variant: "default",
  },
  card: {
    variant: "default",
  },
  badge: {
    variant: "default",
  },
} as const;

// Animation and transition settings
export const animationConfig = {
  duration: {
    fast: 150,
    normal: 300,
    slow: 500,
  },
  easing: {
    default: "cubic-bezier(0.4, 0, 0.2, 1)",
    in: "cubic-bezier(0.4, 0, 1, 1)",
    out: "cubic-bezier(0, 0, 0.2, 1)",
    inOut: "cubic-bezier(0.4, 0, 0.2, 1)",
  },
} as const;

// Responsive breakpoints (matching Tailwind defaults)
export const breakpoints = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  "2xl": 1536,
} as const;

// Z-index scale for layering
export const zIndex = {
  hide: -1,
  auto: "auto",
  base: 0,
  docked: 10,
  dropdown: 1000,
  sticky: 1100,
  banner: 1200,
  overlay: 1300,
  modal: 1400,
  popover: 1500,
  skipLink: 1600,
  toast: 1700,
  tooltip: 1800,
} as const;

// Common spacing values
import { spacing as designSpacing } from "./design-system";
export const spacing = designSpacing;

export default {
  ui: uiConfig,
  components: componentDefaults,
  animation: animationConfig,
  breakpoints,
  zIndex,
  spacing: designSpacing,
} as const;
